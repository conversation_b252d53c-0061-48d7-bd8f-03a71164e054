import { Graph, GraphType, Frame, useGraphConfig } from '@vis/document-core';
import { defineComponent, ref, nextTick, computed, watchEffect } from 'vue';
import type { PropType } from 'vue';
import { useGraph, useAction } from '../../../../../../hooks';
import { VIS_DESIGN_INFINITE_CANVAS } from '../../../../../../models';
import draggable from 'vuedraggable';
import { useDesignStore } from '../../../../../../stores';

/**
 * 图层树
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-layer-item',
  components: { draggable },
  props: {
    nodes: {
      type: Array as PropType<Graph[]>,
      required: true
    },
    selectedIds: {
      type: Array as PropType<string[]>,
      required: true
    },
    expandedMap: {
      type: Map,
      required: true,
      default: () => new Map<string, boolean>()
    },
    itemKey: {
      type: String,
      default: VIS_DESIGN_INFINITE_CANVAS
    },
    dragTargetParentId: {
      type: String,
      default: null
    },
    isSearchHide: {
      type: Boolean,
      default: false
    },
    isEditingStatus: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'update:selected-ids',
    'update:expanded-map',
    'drag-start',
    'drag-end',
    'drag-add',
    'drag-move',
    'context-menu'
  ],
  setup(props, { emit }) {
    const { graphConfig } = useGraphConfig();

    const designStore = useDesignStore();
    const editingGraph = computed(() => designStore.editingGraph.value);

    const { findGraph, activeGraphs } = useGraph();
    const { locationToCanvas } = useAction();

    // #region 双击编辑
    const editingName = ref<string>('');
    const editInputRef = ref<any>(null);

    const isEditing = (node: Graph) => {
      return editingGraph.value && editingGraph.value.id === node.id;
    };

    const handleEdit = (node: Graph) => {
      editingName.value = node.name;
      nextTick(() => {
        editInputRef.value?.focus();
        editInputRef.value?.select();
      });
    };

    const onDoubleClick = (node: Graph, e: Event) => {
      e.stopPropagation();
      designStore.setEditingGraph(node);
      handleEdit(node);
    };

    const onSaveName = (node: Graph) => {
      if (editingName.value.trim() && editingName.value !== node.name) {
        const graph = findGraph(node.id);
        graph!.name = editingName.value.trim();
        node.name = editingName.value.trim();
      }
      onCancelEdit();
    };

    const onCancelEdit = () => {
      designStore.setEditingGraph(null);
      editingName.value = '';
    };

    watchEffect(() => {
      editingGraph.value && setTimeout(() => handleEdit(<Graph>editingGraph.value), 10);
    });
    // #endregion

    // #region 展开收起
    const isExpanded = (node: Graph) => {
      return props.expandedMap.get(node.id) || false;
    };
    const toggleExpand = (node: Graph, e: Event) => {
      if (node.children && node.children.length > 0) {
        emit('update:expanded-map', node);
      }
    };
    const onEmitExpanded = (node: Graph) => {
      emit('update:expanded-map', node);
    };
    // #endregion

    // #region 拖拽
    const onDragStart = (evt: any) => {
      emit('drag-start', evt);
    };

    const onDragEnd = (evt: any) => {
      // console.log(evt.originalEvent, 'evt');
      emit('drag-end', evt);
    };

    const onDragAdd = (evt: CustomEvent) => {
      emit('drag-add', evt);
    };

    const onCheckMove = (evt: any) => {
      emit('drag-move', evt);
    };

    const isDragTargetParent = (node: Graph) => {
      return props.dragTargetParentId === node.id;
    };
    // #endregion

    // #region 选中高亮 - 支持多选
    const isSelected = (node: Graph) => props.selectedIds.includes(node.id);
    const onSelect = (node: Graph) => {
      emit('update:selected-ids', node.id);
    };
    // 转发id
    const onEmitSelected = (id: string) => {
      emit('update:selected-ids', id);
    };
    // #endregion

    // #region 图标
    const isGraphIcon = (graph: Graph) => {
      if (graph.type === GraphType.Frame) {
        return Object.keys(graphConfig).includes((graph as Frame).autoLayout.direction);
      }
      return Object.keys(graphConfig).includes(graph.type);
    };
    const getGraphIcon = (graph: Graph, key: 'icon' | 'name') => {
      if (graph.type === GraphType.Frame) {
        return graphConfig[(graph as Frame).autoLayout.direction][key];
      }
      return graphConfig[graph.type][key];
    };
    // #endregion

    // #region 操作-显示隐藏/锁定解锁
    const hoveredId = ref<string | null>(null);
    const isHovered = (node: Graph) => {
      return hoveredId.value === node.id;
    };
    const isActive = (node: Graph) => {
      return !node.visible || node.locked;
    };
    const onHoverEvent = (evt: any, node: Graph) => {
      hoveredId.value = evt.type === 'mouseenter' ? node.id : null;
    };
    const onVisibleChange = (node: Graph) => {
      const graph = findGraph(node.id);
      graph!.visible = !node.visible;
      node.visible = !node.visible;
    };
    const onLockedChange = (node: Graph) => {
      const graph = findGraph(node.id);
      graph!.locked = !node.locked;
      node.locked = !node.locked;
    };

    /**
     * 将图形定位在画布的中央位置
     * @param node
     */
    const onLocation = (node: Graph) => {
      const graph = findGraph((node as Graph).id);
      graph && activeGraphs([graph]);
      locationToCanvas();
    };
    // #endregion

    // #region 右键菜单
    const onOpenContextMenu = (node: Graph, $event: Event) => {
      emit('context-menu', node, $event);
    };

    // #endregion

    // # region 主容器
    const isMainFrame = (node: Graph) => {
      const page = designStore.active.value.page;
      return page && page.main === node.id;
    };
    // #endregion

    return {
      onDragStart,
      onDragEnd,
      onDragAdd,
      onCheckMove,
      isDragTargetParent,
      isExpanded,
      toggleExpand,
      onEmitExpanded,
      isSelected,
      onSelect,
      onEmitSelected,
      isGraphIcon,
      getGraphIcon,
      isEditing,
      editingName,
      editInputRef,
      onDoubleClick,
      onSaveName,
      onCancelEdit,
      isHovered,
      isActive,
      onHoverEvent,
      onVisibleChange,
      onLockedChange,
      onLocation,
      onOpenContextMenu,
      isMainFrame
    };
  }
});
