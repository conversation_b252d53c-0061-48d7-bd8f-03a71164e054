import { useDocumentStore } from '@vis/document-core';
import { useActionStore, useDesignStore } from '../stores';
import { useAction } from './action';
import { CacheService } from '@hetu/util';
import { computed } from 'vue';
import { Keyboard, osType } from '@hetu/platform-shared';

/**
 * 快捷键
 * <AUTHOR>
 */
export const useShortcutKeys = () => {
  const docStore = useDocumentStore();
  const actionStore = useActionStore();
  const designStore = useDesignStore();

  const { move, hand, deleteGraphs, copyGraph, pasteGraph } = useAction();

  const active = computed(() => designStore.active.value);

  /**
   * 添加快捷键
   */
  const addShortcuts = () => {
    window.addEventListener('keydown', onKeydown);
    window.addEventListener('keyup', onKeyup);

    window.addEventListener('mousedown', onMouseDown);
    window.addEventListener('mouseup', onMouseUp);
  };

  /**
   * 移除快捷键
   */
  const removeShortcuts = () => {
    window.removeEventListener('keydown', onKeydown);
    window.removeEventListener('keyup', onKeyup);

    window.removeEventListener('mousedown', onMouseDown);
    window.removeEventListener('mouseup', onMouseUp);
  };

  const onKeydown = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;
    const target = e.target as HTMLElement;

    if (target) {
      const tagName = target.tagName;
      const exceptTags = ['INPUT', 'TEXTAREA', 'SELECT'];
      if (exceptTags.indexOf(tagName) !== -1) {
        return;
      }
      if (target.contentEditable === 'true') {
        return;
      }
    }

    // 按空格键移动画布
    if (e.code === 'Space') {
      e.preventDefault();
      hand();
    }
    if ((e.metaKey || e.ctrlKey) && e.code === 'KeyS') {
      console.log('保存:', docStore.document.value);
      CacheService.set(`doc_${docStore.document.value.id}`, docStore.document.value);
      // 阻止事件的默认行为
      e.preventDefault();
    }
    if (e.key === 'Delete' || e.key === 'Backspace') {
      deleteGraphs();
    }
    // 复制
    if ((e.metaKey || e.ctrlKey) && e.code === 'KeyC') {
      copyGraph();
    }
    // 粘贴
    if ((e.metaKey || e.ctrlKey) && e.code === 'KeyV') {
      pasteGraph();
    }
  };

  const onKeyup = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;

    // 松开空格键框选组件
    if (e.code === 'Space') {
      move();
    }
  };

  const onMouseDown = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseDown:--------', true);
    }
  };

  const onMouseUp = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseUp:--------', false);
    }
  };

  /**
   * 根据按键编码返回对应值
   * @param keyboard 按键码
   */
  const getKeyCode = (keyboard: number | string | number[]) => {
    let code = '';
    if (osType.isMac) {
      code = '⌘';
    } else {
      code = 'Ctrl';
    }
    if (Array.isArray(keyboard)) {
      let codes: string[] = [];
      keyboard.forEach((item: number) => {
        switch (item) {
          case Keyboard['Numpad+']:
          case Keyboard['Numpad-']:
            codes = [Keyboard[item].replace('Numpad', '')];
            break;
          default:
            codes.push(Keyboard[item]);
            break;
        }
      });
      code = codes.join(' / ');
    } else {
      switch (keyboard) {
        case Keyboard.Control:
          code = 'Ctrl';
          break;
        case Keyboard.ArrowUp:
          code = '↑';
          break;
        case Keyboard.ArrowRight:
          code = '→';
          break;
        case Keyboard.ArrowDown:
          code = '↓';
          break;
        case Keyboard.ArrowLeft:
          code = '←';
          break;
        case 'mousewheel':
          code = '鼠标滚轮';
          break;
        case 'click':
          code = '鼠标单击';
          break;
        default:
          code = Keyboard[keyboard as number];
          break;
      }
    }
    return code;
  };

  return {
    addShortcuts,
    removeShortcuts,
    onKeydown,
    onKeyup,

    getKeyCode
  };
};
