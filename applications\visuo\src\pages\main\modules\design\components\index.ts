import { defineComponent, ref, computed, watch, nextTick, onMounted } from 'vue';
import { VisDesignRuler, VisDesignInfiniteViewer, VisDesignCanvas, VisDesignSelecto } from './content';
import { VisDesignConfig } from './config';
import { VisDesignStore } from './store';
import { useDesignStore } from '../stores';
import { DocumentService } from '@vis/document-core';
import { registerDesignLibraries } from './libraries';
import draggable from 'vuedraggable';
import {
  Block,
  Document,
  Frame,
  GraphType,
  Page,
  WidgetConfig,
  setupGraph,
  useDocumentStore,
  useFrame
} from '@vis/document-core';
import { useGraph, usePage, useWidget } from '../hooks';
import { setupUi } from './ui';
import VisDesignDock from './dock/dock.vue';

export default defineComponent({
  name: 'vis-design',
  components: {
    draggable,
    VisDesignRuler,
    VisDesignInfiniteViewer,
    VisDesignCanvas,
    VisDesignSelecto,
    VisDesignConfig,
    VisDesignStore,
    VisDesignDock
  },
  props: {
    id: String
  },
  setup(props) {
    registerDesignLibraries();
    setupGraph();
    setupUi();
    // const $q = useQuasar();
    // $q.dark.set(true);

    const designStore = useDesignStore();
    const docStore = useDocumentStore();

    const { switchPage } = usePage();

    onMounted(() => {
      initData();
      DocumentService.setFontsStyle();
      DocumentService.loadIconConfig();
    });

    /** 加载文件数据 */
    const initData = async () => {
      if (props.id) {
        DocumentService.info(props.id).then(async (data) => {
          let doc = new Document();
          if (data && Object.keys(data).length) {
            doc = data;
          } else {
            doc.home = doc.children[0].id;
          }
          docStore.document.value = doc;
          switchPage(doc.home);
        });
      }
    };

    //#region 从左侧面板拖拽添加组件

    const rulerState = designStore.rulerState;
    const horizontalGuidesRef = designStore.horizontalGuidesRef;
    const verticalGuidesRef = designStore.verticalGuidesRef;
    const infiniteCanvasRef = designStore.infiniteCanvasRef;
    const canvasState = computed(() => designStore.canvasState.value);

    const dropbox = computed(() => designStore.canvasState.value.dropbox);

    const { handleWidget } = useWidget();
    const { getCanvasPosSize, addBlockToPage, addFrameBySize } = useGraph();
    const libraryList = ref<WidgetConfig[]>([]);

    const page = computed(() => {
      return designStore.active.value.page as Page;
    });

    const addWidgetBlock = (param: { clone: HTMLElement; newIndex: number; originalEvent: DragEvent }) => {
      if (param.clone) {
        const config = libraryList.value[param.newIndex];
        const event = param.originalEvent;

        let { x, y } = getCanvasPosSize(event.clientX, event.clientY);
        // x,y减去组件的宽度/2，是为了让组件中心点在鼠标位置
        x = x - 100 / 2;
        y = y - 100 / 2;
        const { widgetBlock, block: blockConfig } = handleWidget(config);
        const block = Object.assign(new Block(), blockConfig);
        block.width = config.width;
        block.height = config.height;
        block.transform.translate = [x, y];
        block.decoration = widgetBlock.id;
        docStore.document.value.blocks.push(widgetBlock);

        addBlockToPage(block);
        canvasState.value.gridRowCol = undefined;
      }
    };

    //#endregion

    const { prepareSize } = useFrame();

    //#region 左右两侧宽度值
    const leftWidth = computed(() => designStore.leftWidth.value);
    const rightWidth = computed(() => designStore.rightWidth.value);

    //#endregion

    return {
      // 标尺
      rulerState,

      // 画布添加组件
      dropbox,
      libraryList,
      page,
      addWidgetBlock,

      prepareSize,
      addFrameBySize,

      leftWidth,
      rightWidth
    };
  }
});
