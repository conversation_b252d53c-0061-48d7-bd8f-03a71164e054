import { reactive, ref } from 'vue';
import { defineStore } from '@hetu/core';
import { DesignAction, DesignActionGroup } from '../models';
import { Keyboard } from '@hetu/platform-shared';
import type { ContextMenuType } from '../models';

/**
 * 工具栏操作
 * <AUTHOR>
 */
export const useActionStore = defineStore(() => {
  const actions = ref<Record<string, DesignAction>>(
    Object.assign(
      {},
      // dock 工具栏
      new DesignAction('move', '相交选中', [], 'o_near_me', true, 'mouse').record,
      new DesignAction('hand', '平移画布', [], 'o_back_hand', false, 'mouse').record,
      new DesignAction('scale', '等比缩放', [], 'o_photo_size_select_small', false, 'mouse').record,
      new DesignAction('frame', '容器', [], 'hticon-vis-frame ', false, 'mouse').record,
      new DesignAction('textbox', '文本', [], 'hticon-vis-textbox', false, 'mouse').record,
      new DesignAction('design', '设计模式', [], 'hticon-vis-mode-d', true, 'mode').record,
      new DesignAction('dhyana', '禅模式', [], 'hticon-vis-mode-s', false, 'mode').record,
      new DesignAction('node', '节点编程模式', [], 'o_cable', false, 'mode').record,
      new DesignAction('interact', '互动模式', [], 'o_touch_app', false, 'mode').record,
      new DesignAction('ai', 'AI', [], '').record,
      // page 页面
      new DesignAction('addPageToGroup', '添加页面分类', [], '', false, 'page', false).record,
      new DesignAction('deletePage', '删除页面', [], '', false, 'page', false, true).record,
      new DesignAction('clonePage', '创建页面副本', [], '', false, 'page', false).record,
      new DesignAction('renamePage', '重命名', [], '', false, 'page', false).record,
      new DesignAction('setHomePage', '设为主页', [], '', false, 'page', false).record,
      // new DesignAction('addIcon', '添加图标', [], '', false, 'page', false).record,
      // 页面组
      new DesignAction('cancelPageGroup', '取消页面组', [], '', false, 'page', false).record,
      new DesignAction('deletePageGroup', '删除页面组', [], '', false, 'page', false, true).record,
      new DesignAction('renamePageGroup', '重命名', [], '', false, 'page', false).record,
      // new DesignAction('addIcon', '添加图标', [], 'o_settings', false, 'page', false).record,
      // graph 图形
      new DesignAction('copyGraph', '复制', [Keyboard.Control, Keyboard.C], '', false, 'graph', false).record,
      new DesignAction('pasteGraph', '粘贴', [Keyboard.Control, Keyboard.V], '', false, 'graph', false).record,
      new DesignAction('copyGraphId', '复制图形ID', [], '', false, 'graph', false).record,
      new DesignAction('copyGraphStyle', '复制图形样式', [], '', false, 'graph', false).record,
      new DesignAction('copyGraphConfig', '复制图形配置', [], '', false, 'graph', false).record,
      new DesignAction('pasteToMousePosition', '粘贴到鼠标位置', [], '', false, 'graph', false).record,
      new DesignAction('pasteGraphConfig', '粘贴图形配置', [], '', false, 'graph', false).record,
      new DesignAction('pasteAndReplaceGraph', '粘贴并替换', [], '', false, 'graph', false).record,

      new DesignAction('renameGraph', '重命名', [Keyboard.Control, Keyboard.R], '', false, 'graph', false).record,
      new DesignAction('moveUpGraph', '上移一层', [Keyboard.Control, Keyboard[']']], '', false, 'graph', false).record,
      new DesignAction('moveDownGraph', '下移一层', [Keyboard.Control, Keyboard['[']], '', false, 'graph', false)
        .record,
      new DesignAction('moveTopGraph', '移到顶层', [Keyboard[']']], '', false, 'graph', false).record,
      new DesignAction('moveBottomGraph', '移到底层', [Keyboard['[']], '', false, 'graph', false).record,

      new DesignAction('createFrame', '创建容器', [], '', false, 'graph', false).record,
      new DesignAction('cancelFrame', '取消容器', [], '', false, 'graph', false).record,

      new DesignAction('setAsMainFrame', '设置为主容器', [], '', false, 'graph', false).record,
      new DesignAction('setAsCover', '设为封面', [], '', false, 'graph', false).record,

      new DesignAction('locationToCanvas', '定位到画布', [], '', false, 'graph', false).record,
      new DesignAction('locationToNodeProgramming', '定位到节点编程', [], '', false, 'graph', false).record,

      new DesignAction(
        'lockOrUnlockGraph',
        '锁定/解锁图层',
        [Keyboard.Shift, Keyboard.Control, Keyboard.H],
        '',
        false,
        'graph',
        false
      ).record,
      new DesignAction(
        'showOrHideGraph',
        '显示/隐藏图层',
        [Keyboard.Shift, Keyboard.Control, Keyboard.L],
        '',
        false,
        'graph',
        false
      ).record,

      new DesignAction('deleteGraphs', '删除', [], 'hticon-vis-delete', false, 'graph', false).record
    )
  );

  const groups = ref<Record<string, DesignActionGroup>>(
    Object.assign(
      {},
      new DesignActionGroup('copyGraphAs', '复制为', '', false).record,
      new DesignActionGroup('pasteGraphAs', '粘贴为', '', false).record,
      new DesignActionGroup('GraphMove', '移动', '', false).record
    )
  );

  // #region 右键菜单配置
  const contextMenus = reactive<Record<string, ContextMenuType>>({
    page: [['addPageToGroup', 'deletePage', 'clonePage', 'renamePage'], ['setHomePage']],
    pageGroup: [['cancelPageGroup', 'deletePageGroup', 'renamePageGroup']],
    graph: [
      [
        'copyGraph',
        'pasteGraph',
        ['copyGraphAs', [['copyGraphId', 'copyGraphStyle', 'copyGraphConfig']]],
        ['pasteGraphAs', [['pasteGraphConfig', 'pasteAndReplaceGraph']]]
      ],
      ['renameGraph', ['GraphMove', [['moveUpGraph', 'moveDownGraph', 'moveTopGraph', 'moveBottomGraph']]]],
      ['createFrame', 'cancelFrame'],
      ['setAsMainFrame', 'setAsCover'],
      ['locationToCanvas', 'locationToNodeProgramming'],
      ['lockOrUnlockGraph', 'showOrHideGraph'],
      ['deleteGraphs']
    ]
  });
  // #endregion

  // meta + ctrl键是否按下
  const isMetaCtrl = ref(false);

  return {
    actions,
    groups,
    contextMenus,

    isMetaCtrl
  };
});
